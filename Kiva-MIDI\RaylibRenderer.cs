using System;
using System.Collections.Generic;
using System.Numerics;
using System.Runtime.InteropServices;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;
using Texture2D = Kiva_MIDI.RaylibPInvoke.Texture2D;
using RaylibVector2 = Kiva_MIDI.RaylibPInvoke.Vector2;

namespace Kiva_MIDI
{
    /// <summary>
    /// Raylib-based renderer to replace DirectX rendering
    /// </summary>
    public class RaylibRenderer : IDisposable
    {
        [StructLayout(LayoutKind.Sequential)]
        public struct RaylibRenderNote
        {
            public float left;      // X position (left edge)
            public float right;     // X position (right edge)
            public float start;     // Y position (start time, top of note)
            public float end;       // Y position (end time, bottom of note)
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct RaylibRenderKey
        {
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
            public float left;
            public float right;
            public float distance;
            public bool isBlack;
            public bool isPressed;
        }

        private Settings settings;
        private int screenWidth;
        private int screenHeight;
        private float keyboardHeight;
        private RaylibColor backgroundColor = RaylibColor.BLACK;

        // Note texture for efficient rendering
        private bool useNoteTexture = false;
        private string noteTexturePath;
        private Texture2D noteTexture;
        private bool loggedFallback = false;
        private bool loggedTextureUse = false;
        
        // Rendering data
        private List<RaylibRenderNote> noteBuffer = new List<RaylibRenderNote>();
        private RaylibRenderKey[] renderKeys = new RaylibRenderKey[257];
        
        // Keyboard layout data
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        public RaylibRenderer(Settings settings)
        {
            this.settings = settings;
            InitializeKeyboardLayout();
            UpdateColors();
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize black key pattern
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            // Calculate keyboard layout similar to original
            int firstNote = 0;
            int lastNote = 128;

            // Apply key range settings
            switch (settings.General.KeyRange)
            {
                case KeyRangeTypes.Key88:
                    firstNote = 21;
                    lastNote = 109;
                    break;
                case KeyRangeTypes.Key128:
                    firstNote = 0;
                    lastNote = 128;
                    break;
                case KeyRangeTypes.Key256:
                    firstNote = 0;
                    lastNote = 256;
                    break;
                case KeyRangeTypes.KeyMIDI:
                    // Use MIDI file's actual range
                    firstNote = settings.General.FirstKey;
                    lastNote = settings.General.LastKey + 1;
                    break;
                case KeyRangeTypes.KeyDynamic:
                    // Use dynamic range (will be calculated elsewhere)
                    firstNote = settings.General.FirstKey;
                    lastNote = settings.General.LastKey + 1;
                    break;
                case KeyRangeTypes.Custom:
                    firstNote = Math.Max(0, Math.Min(255, settings.General.CustomFirstKey));
                    lastNote = Math.Max(firstNote + 1, Math.Min(256, settings.General.CustomLastKey + 1));
                    break;
            }

            CalculateKeyPositions(firstNote, lastNote);
        }

        private void CalculateKeyPositions(int firstNote, int lastNote)
        {
            // Use the original Kiva MIDI key positioning algorithm
            int[] keynum = new int[257];

            // Calculate key numbers for white and black keys separately
            int blackKeyNum = 0;
            int whiteKeyNum = 0;

            for (int i = 0; i < 257; i++)
            {
                if (blackKeys[i])
                {
                    keynum[i] = blackKeyNum++;
                }
                else
                {
                    keynum[i] = whiteKeyNum++;
                }
            }

            // Original Kiva MIDI positioning parameters
            double blackKeyScale = 0.65;
            double offset2set = 0.3;
            double offset3set = 0.5;

            double knmfn = keynum[firstNote];
            double knmln = keynum[lastNote - 1];
            if (blackKeys[firstNote]) knmfn = keynum[firstNote - 1] + 0.5;
            if (blackKeys[lastNote - 1]) knmln = keynum[lastNote] - 0.5;

            fullLeft = 0;
            fullRight = 1;
            fullWidth = 1;

            for (int i = 0; i < 257; i++)
            {
                if (!blackKeys[i])
                {
                    // White key positioning (original algorithm)
                    x1array[i] = (keynum[i] - knmfn) / (knmln - knmfn + 1);
                    wdtharray[i] = 1.0 / (knmln - knmfn + 1);
                }
                else
                {
                    // Black key positioning (original algorithm)
                    int _i = i + 1;
                    double wdth = blackKeyScale / (knmln - knmfn + 1);
                    int bknum = keynum[i] % 5;
                    double offset = wdth / 2;

                    if (bknum == 0) offset += offset * offset2set;
                    if (bknum == 2) offset += offset * offset3set;
                    if (bknum == 1) offset -= offset * offset2set;
                    if (bknum == 4) offset -= offset * offset3set;

                    x1array[i] = (keynum[_i] - knmfn) / (knmln - knmfn + 1) - offset;
                    wdtharray[i] = wdth;
                }

                // Set render key positions
                renderKeys[i].left = (float)x1array[i];
                renderKeys[i].right = (float)(x1array[i] + wdtharray[i]);
                renderKeys[i].isBlack = blackKeys[i];
            }
        }

        public void SetScreenSize(int width, int height)
        {
            screenWidth = width;
            screenHeight = height;
            // Calculate keyboard height based on current settings
            UpdateKeyboardHeight();
        }

        private void UpdateKeyboardHeight()
        {
            // Calculate actual keyboard height based on style
            if (settings.General.KeyboardStyle == KeyboardStyle.None)
            {
                keyboardHeight = 0; // No keyboard = no height
            }
            else if (settings.General.KeyboardStyle == KeyboardStyle.Small)
            {
                keyboardHeight = screenHeight * 0.15f * 0.7f; // Small keyboard
            }
            else
            {
                keyboardHeight = screenHeight * 0.15f; // Normal keyboard
            }
        }

        public void UpdateKeyboardSettings()
        {
            // Update keyboard height based on new settings
            UpdateKeyboardHeight();
            // Reinitialize keyboard layout with current settings
            InitializeKeyboardLayout();
        }

        public void UpdateColors()
        {
            // Update background color from settings
            var bgColor = settings.General.BackgroundColor;
            backgroundColor = new RaylibColor(bgColor.R, bgColor.G, bgColor.B, bgColor.A);
        }

        public void BeginFrame()
        {
            Raylib.ClearBackground(backgroundColor);
            noteBuffer.Clear();
        }

        public void AddNote(float left, float right, float start, float end, RaylibColor colorLeft, RaylibColor colorRight)
        {
            noteBuffer.Add(new RaylibRenderNote
            {
                left = left,
                right = right,
                start = start,
                end = end,
                colorLeft = colorLeft,
                colorRight = colorRight
            });
        }

        public void AddNoteByKey(int keyIndex, float start, float end, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // Use the same width calculations as the keyboard layout
            float noteLeft = (float)x1array[keyIndex];
            float noteRight = noteLeft + (float)wdtharray[keyIndex];

            noteBuffer.Add(new RaylibRenderNote
            {
                left = noteLeft,
                right = noteRight,
                start = start,
                end = end,
                colorLeft = colorLeft,
                colorRight = colorRight
            });
        }

        public void UpdateKey(int keyIndex, RaylibColor colorLeft, RaylibColor colorRight, bool pressed, float distance)
        {
            if (keyIndex >= 0 && keyIndex < renderKeys.Length)
            {
                renderKeys[keyIndex].colorLeft = colorLeft;
                renderKeys[keyIndex].colorRight = colorRight;
                renderKeys[keyIndex].isPressed = pressed;
                renderKeys[keyIndex].distance = distance;
            }
        }

        public void RenderNotes()
        {
            float noteAreaHeight = screenHeight - keyboardHeight;

            foreach (var note in noteBuffer)
            {
                RenderKivaMIDINote(note, noteAreaHeight);
            }
        }

        private void RenderKivaMIDINote(RaylibRenderNote note, float noteAreaHeight)
        {
            // Convert normalized coordinates to screen coordinates
            float noteLeft = note.left * screenWidth;
            float noteRight = note.right * screenWidth;

            // Convert time coordinates to screen Y coordinates (flipped vertically)
            // Notes now rise from bottom (future) to top (present) like a piano roll
            // start is the note beginning (bottom), end is note ending (top)
            float noteTop = noteAreaHeight - (note.end * noteAreaHeight);
            float noteBottom = noteAreaHeight - (note.start * noteAreaHeight);

            // Ensure notes are rendered in the correct area
            if (noteTop < 0) noteTop = 0;
            if (noteBottom > noteAreaHeight) noteBottom = noteAreaHeight;
            if (noteTop >= noteBottom) return; // Skip invalid notes

            // Calculate minimum note height based on the 0.008 size setting
            // This ensures very short notes are always visible
            float noteHeight = noteBottom - noteTop;
            float minNoteHeight = noteAreaHeight * 0.008f; // 0.8% of note area height as minimum
            if (noteHeight < minNoteHeight)
            {
                // Expand the note to minimum height, keeping it centered on original position
                float center = (noteTop + noteBottom) / 2;
                noteTop = center - minNoteHeight / 2;
                noteBottom = center + minNoteHeight / 2;

                // Ensure we don't go out of bounds
                if (noteTop < 0)
                {
                    noteTop = 0;
                    noteBottom = minNoteHeight;
                }
                if (noteBottom > noteAreaHeight)
                {
                    noteBottom = noteAreaHeight;
                    noteTop = noteAreaHeight - minNoteHeight;
                }
            }

            // ALWAYS use texture rendering - no fallback allowed
            if (useNoteTexture && noteTexture.id > 0)
            {
                // Debug: Log when using texture rendering (only log once)
                if (!loggedTextureUse)
                {
                    Console.WriteLine($"Using note texture rendering (texture.id: {noteTexture.id}, size: {noteTexture.width}x{noteTexture.height})");
                    loggedTextureUse = true;
                }
                RenderNoteWithTexture(note, noteLeft, noteTop, noteRight - noteLeft, noteBottom - noteTop);
                return;
            }

            // If texture is not available, don't render anything (force texture-only rendering)
            if (!loggedFallback)
            {
                Console.WriteLine($"ERROR: Texture not available! Cannot render notes without texture (useNoteTexture: {useNoteTexture}, texture.id: {(useNoteTexture ? noteTexture.id : 0)})");
                loggedFallback = true;
            }
            // If texture is not available, don't render anything - TEXTURE-ONLY rendering
        }



        public void RenderKeyboard()
        {
            // Check keyboard style setting
            if (settings.General.KeyboardStyle == KeyboardStyle.None)
                return;

            // Use the pre-calculated keyboard height
            int kbHeight = (int)keyboardHeight;

            // Position keyboard at the very bottom of the screen
            int keyboardY = screenHeight - kbHeight;

            // Step 1: Render keyboard bar (top section)
            RenderKivaKeyboardBar(keyboardY, kbHeight);

            // Use the calculated key range from settings (FirstKey to LastKey)
            int firstKey = settings.General.FirstKey;
            int lastKey = settings.General.LastKey;

            // Step 2: Render white keys first (background layer)
            for (int i = firstKey; i <= lastKey; i++)
            {
                if (i < renderKeys.Length && !blackKeys[i] && renderKeys[i].right > renderKeys[i].left)
                {
                    RenderKivaWhiteKey(i, keyboardY, kbHeight);
                }
            }

            // Step 3: Render black keys on top (foreground layer)
            for (int i = firstKey; i <= lastKey; i++)
            {
                if (i < renderKeys.Length && blackKeys[i] && renderKeys[i].right > renderKeys[i].left)
                {
                    RenderKivaBlackKey(i, keyboardY, kbHeight);
                }
            }
        }

        private void RenderKivaKeyboardBar(int keyboardY, int kbHeight)
        {
            // Recreate KeyboardBig.fx GS_Bar shader (lines 84-122)
            try
            {
                var barColor = ColorFromWpfColor(settings.General.BarColor);

                // Main bar dimensions (make it shorter)
                float barHeightRatio = 0.06f; // Reduced from 0.08f to make bar shorter
                int barHeight = Math.Max(4, (int)(kbHeight * barHeightRatio));
                int barTop = keyboardY - barHeight;
                int barBottom = keyboardY;

                // Top section (brighter) - lines 92-96
                Raylib.DrawRectangle(0, barTop, screenWidth, barHeight, barColor);

                // Bottom section (darker - 80% of original) - lines 94-95
                RaylibColor bottomBarColor = new RaylibColor(
                    (byte)(barColor.r * 0.8f),
                    (byte)(barColor.g * 0.8f),
                    (byte)(barColor.b * 0.8f),
                    barColor.a
                );
                int bottomHeight = (int)(barHeight * 0.06f);
                Raylib.DrawRectangle(0, barBottom - bottomHeight, screenWidth, bottomHeight, bottomBarColor);

                // Shadow effects (lines 103-121) - transparent black gradients
                // Top shadow (lines 107-111)
                int shadowHeight = (int)(kbHeight * 0.03f);
                for (int i = 0; i < shadowHeight; i++)
                {
                    float alpha = (float)i / shadowHeight * 0.4f;
                    RaylibColor shadowColor = new RaylibColor(0, 0, 0, (byte)(alpha * 255));
                    Raylib.DrawRectangle(0, barTop - shadowHeight + i, screenWidth, 1, shadowColor);
                }

                // Bottom shadow (lines 113-121)
                for (int i = 0; i < shadowHeight; i++)
                {
                    float alpha = 0.4f - (float)i / shadowHeight * 0.4f;
                    RaylibColor shadowColor = new RaylibColor(0, 0, 0, (byte)(alpha * 255));
                    Raylib.DrawRectangle(0, barBottom + i, screenWidth, 1, shadowColor);
                }
            }
            catch (Exception ex)
            {
                // Fallback to simple bar
                var fallbackColor = new RaylibColor(0, 104, 201, 255);
                int fallbackHeight = (int)(kbHeight * 0.06f);
                Raylib.DrawRectangle(0, keyboardY - fallbackHeight, screenWidth, fallbackHeight, fallbackColor);
            }
        }

        private void RenderKivaWhiteKey(int keyIndex, int keyboardY, int kbHeight)
        {
            var key = renderKeys[keyIndex];

            // Round positions to prevent artifacts
            int left = (int)Math.Round(key.left * screenWidth);
            int right = (int)Math.Round(key.right * screenWidth);
            int width = right - left;

            // Bounds checking
            if (width <= 0 || left < 0 || right > screenWidth) return;

            // Full height keyboard - keys touch the bottom of the screen
            int height = kbHeight; // Use full keyboard height
            int top = keyboardY;
            int bottom = screenHeight; // Keys extend to the very bottom of the screen

            // Add push down effect for pressed keys
            bool pressed = key.isPressed;
            int originalTop = top; // Store original position to fill gap
            if (pressed)
            {
                int pushDown = Math.Max(1, (int)(height * 0.03f)); // 3% push down
                top += pushDown;
                // FIX: Keep bottom at screen bottom - don't move it down like before
                // bottom stays at screenHeight to maintain contact with screen bottom
            }

            // Bevel calculations with proper rounding (keep original style)
            int bevelSize = Math.Max(1, (int)(height * 0.04f));
            int itop = top + bevelSize;
            int ibottom = bottom - (int)(bevelSize * 1.4f);
            if (pressed) ibottom = bottom - bevelSize / 3;

            // Color calculations with alpha blending (lines 145-151) - ORIGINAL STYLE
            RaylibColor baseWhite = new RaylibColor(255, 255, 255, 255);
            RaylibColor colorl = BlendWithAlpha(key.colorLeft, baseWhite);
            RaylibColor colorr = BlendWithAlpha(key.colorRight, baseWhite);

            // Center section - main key body with gradient (slightly brighter) - ORIGINAL STYLE
            RaylibColor centerLeft = new RaylibColor(
                (byte)(colorl.r * 0.85f), // Slightly brighter (85% instead of 80%)
                (byte)(colorl.g * 0.85f),
                (byte)(colorl.b * 0.85f),
                255
            );
            RaylibColor centerRight = new RaylibColor(
                (byte)(colorr.r * 0.85f),
                (byte)(colorr.g * 0.85f),
                (byte)(colorr.b * 0.85f),
                255
            );
            DrawGradientRectangle(left, itop, width, ibottom - itop, centerLeft, centerRight);

            // Bottom section - darker bottom part for better definition - ORIGINAL STYLE
            RaylibColor bottomColor = new RaylibColor(
                (byte)(colorr.r * 0.65f), // Slightly darker (65% instead of 70%)
                (byte)(colorr.g * 0.65f),
                (byte)(colorr.b * 0.65f),
                255
            );
            Raylib.DrawRectangle(left, ibottom, width, bottom - ibottom, bottomColor);

            // FIX: Fill any gap above the key when pressed (prevents white pixels) - ORIGINAL STYLE
            if (pressed && originalTop < top)
            {
                // Fill the gap with a slightly darker version of the key color
                RaylibColor gapFillColor = new RaylibColor(
                    (byte)(colorl.r * 0.9f),
                    (byte)(colorl.g * 0.9f),
                    (byte)(colorl.b * 0.9f),
                    255
                );
                // Ensure complete coverage of the gap area
                Raylib.DrawRectangle(left, originalTop, width, top - originalTop, gapFillColor);
            }

            // FIX: Top section - use center color instead of raw colorl to prevent white artifacts
            if (itop > top)
            {
                // Use the same color as center section to prevent white artifacts
                Raylib.DrawRectangle(left, top, width, itop - top, centerLeft);
            }

            // FIX: Right edge separator - dark line (only if key is wide enough) - ORIGINAL STYLE
            if (width > 2)
            {
                int rightEdge = right - Math.Max(1, screenWidth / 1200); // Scale with screen width
                RaylibColor edgeColor = new RaylibColor(51, 51, 51, 255); // 0.2 * 255 = 51
                if (rightEdge > left)
                {
                    // FIX: Ensure right edge draws from original top to bottom (complete outline)
                    int edgeTop = pressed ? originalTop : top; // Use original top when pressed for complete outline
                    Raylib.DrawRectangle(rightEdge, edgeTop, right - rightEdge, bottom - edgeTop, edgeColor);
                }
            }
        }

        private void RenderWhiteKey(int keyIndex, float keyboardY)
        {
            var key = renderKeys[keyIndex];

            float x = key.left * screenWidth;
            float width = (key.right - key.left) * screenWidth;
            float height = keyboardHeight * 0.94f; // Match original shader height

            // Calculate beveled edges (similar to original shader)
            float bevel = 0.04f * height;
            float innerTop = keyboardY + bevel;
            float innerBottom = keyboardY + height - bevel * 1.4f;

            if (key.isPressed)
            {
                innerBottom = keyboardY + height - bevel / 3;
            }

            RaylibColor baseColor = new RaylibColor(255, 255, 255, 255);
            RaylibColor topColor = baseColor;
            RaylibColor bottomColor = new RaylibColor(
                (byte)(baseColor.r * 0.8f),
                (byte)(baseColor.g * 0.8f),
                (byte)(baseColor.b * 0.8f),
                255
            );

            // Blend with note color if present
            if (key.colorLeft.a > 0)
            {
                topColor = BlendColors(topColor, key.colorLeft);
                bottomColor = BlendColors(bottomColor, key.colorLeft);
            }

            // Draw main key body with gradient effect
            DrawGradientRectangle((int)x, (int)innerTop, (int)width, (int)(innerBottom - innerTop), topColor, bottomColor);

            // Draw bottom section
            RaylibColor bottomSectionColor = new RaylibColor(
                (byte)(bottomColor.r * 0.7f),
                (byte)(bottomColor.g * 0.7f),
                (byte)(bottomColor.b * 0.7f),
                255
            );
            Raylib.DrawRectangle((int)x, (int)innerBottom, (int)width, (int)(keyboardY + height - innerBottom), bottomSectionColor);

            // Draw right edge (darker)
            float rightEdgeX = x + width - 1;
            Raylib.DrawRectangle((int)rightEdgeX, (int)keyboardY, 1, (int)height, new RaylibColor(51, 51, 51, 255));
        }

        private void RenderKivaBlackKey(int keyIndex, int keyboardY, int kbHeight)
        {
            var key = renderKeys[keyIndex];

            // Round positions to prevent artifacts
            int left = (int)Math.Round(key.left * screenWidth);
            int right = (int)Math.Round(key.right * screenWidth);
            int width = right - left;

            // Minimum width check and bounds validation
            if (width <= 2 || left < 0 || right > screenWidth) return;

            // Black keys render over the blue bar with push down effect
            // Use full keyboard height for white keys, black keys are 60% of that
            int whiteKeyHeight = kbHeight; // White keys use full height
            int blackKeyHeight = (int)(whiteKeyHeight * 0.6f); // Black keys are 60% of white key height

            // Calculate bar height to determine overlap
            int barHeight = Math.Max(4, (int)(kbHeight * 0.06f));
            int barTop = keyboardY - barHeight;

            // Black keys start above the keyboard area to overlap the bar
            int top = barTop + (int)(barHeight * 0.3f); // Start 30% into the bar
            int bottom = keyboardY + blackKeyHeight;

            // Add push down effect for pressed keys (like white keys)
            bool pressed = key.isPressed;
            if (pressed)
            {
                int pushDown = Math.Max(1, (int)(blackKeyHeight * 0.05f)); // 5% push down
                top += pushDown;
                bottom += pushDown;
            }

            // Simplified beveling - reduce bevel size to prevent weird outlines
            int bevelSize = Math.Max(1, (int)(blackKeyHeight * 0.05f)); // Reduced from 0.08f to 0.05f
            int ileft = left + bevelSize;
            int iright = right - bevelSize;
            int itop = top + bevelSize;
            int ibottom = bottom - bevelSize;

            // Ensure inner dimensions are valid
            if (ileft >= iright || itop >= ibottom || width <= 4)
            {
                // Fallback to simple rectangle if beveling would create invalid dimensions
                ileft = left;
                iright = right;
                itop = top;
                ibottom = bottom;
                bevelSize = 0;
            }



            // Color calculations - blend note colors with brighter dark base (1.3x brighter)
            RaylibColor baseColor = new RaylibColor(39, 39, 39, 255); // 1.3x brighter: 30 * 1.3 = 39
            RaylibColor keyColor = baseColor;

            if (key.colorLeft.a > 0)
            {
                // Blend note color more subtly for black keys
                float alpha = key.colorLeft.a / 255.0f * 0.7f;
                keyColor = new RaylibColor(
                    (byte)Math.Min(255, key.colorLeft.r * alpha + baseColor.r * (1 - alpha)),
                    (byte)Math.Min(255, key.colorLeft.g * alpha + baseColor.g * (1 - alpha)),
                    (byte)Math.Min(255, key.colorLeft.b * alpha + baseColor.b * (1 - alpha)),
                    255
                );
            }

            // Adjust for pressed state
            if (pressed)
            {
                keyColor = new RaylibColor(
                    (byte)(keyColor.r * 0.6f),
                    (byte)(keyColor.g * 0.6f),
                    (byte)(keyColor.b * 0.6f),
                    255
                );
            }

            // Draw a simple black key if it's very narrow
            if (width <= 5)
            {
                // Simple black key for very narrow keys
                RaylibColor simpleColor = new RaylibColor(30, 30, 30, 255);
                Raylib.DrawRectangle(left, top, width, bottom - top, simpleColor);
                return;
            }

            // Draw the black key as a single solid rectangle first (base layer)
            Raylib.DrawRectangle(left, top, width, bottom - top, keyColor);

            // Only add 3D effects if there's enough space
            if (bevelSize > 0 && width > 4)
            {
                // Main key body - center section (slightly darker)
                RaylibColor centerColor = new RaylibColor(
                    (byte)(keyColor.r * 0.9f),
                    (byte)(keyColor.g * 0.9f),
                    (byte)(keyColor.b * 0.9f),
                    255
                );
                Raylib.DrawRectangle(ileft, itop, iright - ileft, ibottom - itop, centerColor);

                // Top highlight (if not pressed)
                if (!pressed)
                {
                    RaylibColor highlight = new RaylibColor(
                        (byte)Math.Min(255, keyColor.r * 1.15f),
                        (byte)Math.Min(255, keyColor.g * 1.15f),
                        (byte)Math.Min(255, keyColor.b * 1.15f),
                        255
                    );
                    Raylib.DrawRectangle(left, top, width, bevelSize, highlight);
                }
                else
                {
                    // Darker top section when pressed
                    RaylibColor pressedTop = new RaylibColor(
                        (byte)(keyColor.r * 0.8f),
                        (byte)(keyColor.g * 0.8f),
                        (byte)(keyColor.b * 0.8f),
                        255
                    );
                    Raylib.DrawRectangle(left, top, width, bevelSize, pressedTop);
                }

                // Bottom shadow (darker)
                if (bottom > ibottom)
                {
                    RaylibColor bottomShadow = new RaylibColor(
                        (byte)(keyColor.r * 0.7f),
                        (byte)(keyColor.g * 0.7f),
                        (byte)(keyColor.b * 0.7f),
                        255
                    );
                    Raylib.DrawRectangle(left, ibottom, width, bottom - ibottom, bottomShadow);
                }
            }

            // No border lines - they cause the weird outline effect
        }

        private void RenderBlackKey(int keyIndex, float keyboardY)
        {
            var key = renderKeys[keyIndex];

            float x = key.left * screenWidth;
            float width = (key.right - key.left) * screenWidth;
            float height = keyboardHeight * 0.94f;
            float aspect = (float)screenHeight / screenWidth;

            // Calculate dimensions similar to original shader
            float bevel = 0.015f * height;
            float top = keyboardY;
            float bottom = keyboardY + height * 0.35f + bevel * height;

            float innerLeft = x + bevel * height * aspect;
            float innerRight = x + width - bevel * height * aspect;
            float innerTop = top + bevel * height * 2.5f;
            float innerBottom = bottom + bevel * height;

            if (key.isPressed)
            {
                innerTop = top;
            }
            else
            {
                innerBottom = bottom + bevel * height * 2.5f;
            }

            RaylibColor baseColor = new RaylibColor(30, 30, 30, 255);

            // Blend with note color if present
            if (key.colorLeft.a > 0)
            {
                baseColor = BlendColors(baseColor, key.colorLeft);
            }

            // Draw center section
            RaylibColor centerColor = key.isPressed ?
                new RaylibColor((byte)(baseColor.r * 0.7f), (byte)(baseColor.g * 0.7f), (byte)(baseColor.b * 0.7f), 255) :
                baseColor;
            Raylib.DrawRectangle((int)innerLeft, (int)innerTop, (int)(innerRight - innerLeft), (int)(innerBottom - innerTop), centerColor);

            // Draw left edge
            RaylibColor leftColor = key.isPressed ?
                new RaylibColor((byte)(baseColor.r * 0.88f), (byte)(baseColor.g * 0.88f), (byte)(baseColor.b * 0.88f), 255) :
                new RaylibColor((byte)(baseColor.r * 1.3f), (byte)(baseColor.g * 1.3f), (byte)(baseColor.b * 1.3f), 255);
            Raylib.DrawRectangle((int)x, (int)top, (int)(innerLeft - x), (int)(bottom - top), leftColor);

            // Draw right edge
            RaylibColor rightColor = key.isPressed ?
                new RaylibColor((byte)(baseColor.r * 0.78f), (byte)(baseColor.g * 0.78f), (byte)(baseColor.b * 0.78f), 255) :
                new RaylibColor((byte)(baseColor.r * 1.3f), (byte)(baseColor.g * 1.3f), (byte)(baseColor.b * 1.3f), 255);
            Raylib.DrawRectangle((int)innerRight, (int)top, (int)(x + width - innerRight), (int)(bottom - top), rightColor);

            // Draw bottom edge
            RaylibColor bottomColor = key.isPressed ?
                new RaylibColor((byte)(baseColor.r * 0.88f), (byte)(baseColor.g * 0.88f), (byte)(baseColor.b * 0.88f), 255) :
                new RaylibColor((byte)(baseColor.r * 1.19f), (byte)(baseColor.g * 1.19f), (byte)(baseColor.b * 1.19f), 255);
            Raylib.DrawRectangle((int)innerLeft, (int)innerBottom, (int)(innerRight - innerLeft), (int)(bottom - innerBottom), bottomColor);
        }

        private void RenderKeyboardBar(float keyboardY)
        {
            var barColor = ColorFromWpfColor(settings.General.BarColor);
            float barHeight = keyboardHeight * 0.06f;

            Raylib.DrawRectangle(0, (int)(keyboardY - barHeight), screenWidth, (int)barHeight, barColor);
        }

        private void DrawGradientRectangle(int x, int y, int width, int height, RaylibColor topColor, RaylibColor bottomColor)
        {
            // Simple gradient approximation by drawing horizontal lines
            for (int i = 0; i < height; i++)
            {
                float t = (float)i / height;
                RaylibColor lineColor = new RaylibColor(
                    (byte)(topColor.r * (1 - t) + bottomColor.r * t),
                    (byte)(topColor.g * (1 - t) + bottomColor.g * t),
                    (byte)(topColor.b * (1 - t) + bottomColor.b * t),
                    (byte)(topColor.a * (1 - t) + bottomColor.a * t)
                );
                Raylib.DrawRectangle(x, y + i, width, 1, lineColor);
            }
        }

        private RaylibColor BlendColors(RaylibColor base1, RaylibColor overlay)
        {
            float alpha = overlay.a / 255.0f;
            return new RaylibColor(
                (byte)(base1.r * (1 - alpha) + overlay.r * alpha),
                (byte)(base1.g * (1 - alpha) + overlay.g * alpha),
                (byte)(base1.b * (1 - alpha) + overlay.b * alpha),
                255
            );
        }

        private RaylibColor ColorFromSystemColor(System.Drawing.Color systemColor)
        {
            return new RaylibColor(systemColor.R, systemColor.G, systemColor.B, systemColor.A);
        }

        private RaylibColor ColorFromWpfColor(System.Windows.Media.Color wpfColor)
        {
            return new RaylibColor(wpfColor.R, wpfColor.G, wpfColor.B, wpfColor.A);
        }

        private bool IsBlackNote(int noteNumber)
        {
            int n = noteNumber % 12;
            return n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
        }

        private RaylibColor BlendWithAlpha(RaylibColor color, RaylibColor baseColor)
        {
            // Shader: float4 colorl = float4(colorlConv.xyz * colorlConv.w + (1 - colorlConv.w), 1);
            float alpha = color.a / 255.0f;
            return new RaylibColor(
                (byte)(color.r * alpha + baseColor.r * (1 - alpha)),
                (byte)(color.g * alpha + baseColor.g * (1 - alpha)),
                (byte)(color.b * alpha + baseColor.b * (1 - alpha)),
                255
            );
        }

        private RaylibColor DimColor(RaylibColor color, float dimValue)
        {
            // Shader: dim() function adds dimValue to RGB
            return new RaylibColor(
                (byte)Math.Max(0, Math.Min(255, color.r + dimValue * 255)),
                (byte)Math.Max(0, Math.Min(255, color.g + dimValue * 255)),
                (byte)Math.Max(0, Math.Min(255, color.b + dimValue * 255)),
                color.a
            );
        }

        private void DrawQuad(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4,
                             RaylibColor c1, RaylibColor c2, RaylibColor c3, RaylibColor c4)
        {
            // Simple quad approximation using two triangles with average colors
            RaylibColor avgColor = new RaylibColor(
                (byte)((c1.r + c2.r + c3.r + c4.r) / 4),
                (byte)((c1.g + c2.g + c3.g + c4.g) / 4),
                (byte)((c1.b + c2.b + c3.b + c4.b) / 4),
                (byte)((c1.a + c2.a + c3.a + c4.a) / 4)
            );

            // Find bounding rectangle and fill it
            int minX = Math.Min(Math.Min(x1, x2), Math.Min(x3, x4));
            int maxX = Math.Max(Math.Max(x1, x2), Math.Max(x3, x4));
            int minY = Math.Min(Math.Min(y1, y2), Math.Min(y3, y4));
            int maxY = Math.Max(Math.Max(y1, y2), Math.Max(y3, y4));

            Raylib.DrawRectangle(minX, minY, maxX - minX, maxY - minY, avgColor);
        }

        public void EndFrame()
        {
            // Frame rendering complete - ensure note buffer is cleared for next frame
            // This prevents accumulation that could cause performance issues
            noteBuffer.Clear();
        }

        /// <summary>
        /// Generate a note.png file with white note in the exact same style as the current note rendering
        /// </summary>
        public void GenerateNotePNG()
        {
            try
            {
                // Check if note.png already exists to avoid regenerating it every time
                string exeDir = System.IO.Path.GetDirectoryName(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                string pngPath = System.IO.Path.Combine(exeDir, "note.png");

                if (System.IO.File.Exists(pngPath))
                {
                    Console.WriteLine($"note.png already exists at: {pngPath} - skipping generation");
                    // Still try to load the texture
                    noteTexturePath = pngPath;
                    TryLoadNoteTexture();
                    return;
                }
                // Note dimensions - 100x200 for better quality
                int noteWidth = 100;
                int noteHeight = 200;

                // Create bitmap with transparent background
                using (var bitmap = new System.Drawing.Bitmap(noteWidth, noteHeight, System.Drawing.Imaging.PixelFormat.Format32bppArgb))
                using (var graphics = System.Drawing.Graphics.FromImage(bitmap))
                {
                    // Enable anti-aliasing for smooth edges
                    graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                    // Clear with transparent background
                    graphics.Clear(System.Drawing.Color.Transparent);

                    // Create a white note with the exact same style calculations as RenderKivaMIDINote
                    RaylibRenderNote whiteNote = new RaylibRenderNote
                    {
                        left = 0.0f,
                        right = 1.0f,
                        start = 0.0f,
                        end = 1.0f,
                        colorLeft = new RaylibColor(255, 255, 255, 255),  // Pure white
                        colorRight = new RaylibColor(255, 255, 255, 255)  // Pure white
                    };

                    // Render the note using System.Drawing with the exact same style calculations
                    RenderNoteToSystemDrawing(graphics, whiteNote, noteWidth, noteHeight);

                    // Export as PNG in the same directory as the executable
                    string executableDir = System.IO.Path.GetDirectoryName(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                    string notePngPath = System.IO.Path.Combine(executableDir, "note.png");

                    bitmap.Save(notePngPath, System.Drawing.Imaging.ImageFormat.Png);

                    Console.WriteLine($"Successfully generated note.png at: {notePngPath} (100x200 with much darker borders, 2x enhanced gradient)");

                    // Store the path and try to load the texture
                    noteTexturePath = notePngPath;
                    TryLoadNoteTexture();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating note.png: {ex.Message}");
            }
        }

        /// <summary>
        /// Render a note to System.Drawing.Graphics using the exact same style as RenderKivaMIDINote
        /// </summary>
        private void RenderNoteToSystemDrawing(System.Drawing.Graphics graphics, RaylibRenderNote note, int imageWidth, int imageHeight)
        {
            // Convert normalized coordinates to image coordinates
            float noteLeft = note.left * imageWidth;
            float noteRight = note.right * imageWidth;

            // Convert time coordinates to image Y coordinates
            float noteTop = imageHeight - (note.end * imageHeight);
            float noteBottom = imageHeight - (note.start * imageHeight);

            // Ensure notes are rendered correctly
            if (noteTop < 0) noteTop = 0;
            if (noteBottom > imageHeight) noteBottom = imageHeight;
            if (noteTop >= noteBottom) return; // Skip invalid notes

            float width = noteRight - noteLeft;
            float height = noteBottom - noteTop;

            // Apply minimum size constraints (consistent with texture rendering)
            float minSize = Math.Max(2, imageHeight * 0.001f); // At least 2 pixels or 0.1% of image height
            if (width < minSize) width = minSize;
            if (height < minSize) height = minSize;

            if (width <= 0 || height <= 0) return;

            // Calculate border thickness exactly like the original shader (Notes.fx lines 51-53)
            float noteBorder = 0.00091f;
            float noteBorderH = (float)Math.Round(noteBorder * imageWidth) / imageWidth * imageWidth;
            float noteBorderV = (float)Math.Round(noteBorder * imageHeight) / imageHeight * imageHeight / (imageHeight / (float)imageWidth);

            // Ensure minimum border size (original calculation)
            noteBorderH = Math.Max(1, noteBorderH);
            noteBorderV = Math.Max(1, noteBorderV);

            // Extract original colors (matching shader color extraction)
            RaylibColor colorL = note.colorLeft;
            RaylibColor colorR = note.colorRight;

            // Step 1: Draw dark background/shadow (matching shader lines 55-60)
            // Make the outer borders MUCH darker than the inner note for proper contrast
            // cl.xyz *= 0.2f; cl.xyz -= 0.05f; cl.xyz = clamp(cl.xyz, 0, 1);
            System.Drawing.Color shadowColorL = System.Drawing.Color.FromArgb(
                colorL.a,
                Math.Max(0, Math.Min(255, (int)(colorL.r * 0.05f - 50))), // Much darker outer border
                Math.Max(0, Math.Min(255, (int)(colorL.g * 0.05f - 50))),
                Math.Max(0, Math.Min(255, (int)(colorL.b * 0.05f - 50)))
            );
            System.Drawing.Color shadowColorR = System.Drawing.Color.FromArgb(
                colorR.a,
                Math.Max(0, Math.Min(255, (int)(colorR.r * 0.05f - 50))), // Much darker outer border
                Math.Max(0, Math.Min(255, (int)(colorR.g * 0.05f - 50))),
                Math.Max(0, Math.Min(255, (int)(colorR.b * 0.05f - 50)))
            );

            // Draw shadow background with gradient (matching shader triangles 1 & 2)
            DrawGradientRectangle(graphics, noteLeft, noteTop, width, height, shadowColorL, shadowColorR);

            // Step 2: Draw bright inner area (matching shader lines 88-93)
            // cl.xyz += 0.1f; cr.xyz -= 0.3f; cl.xyz = clamp(cl.xyz, 0, 1);
            float borderTop = noteTop + noteBorderV;
            float borderBottom = noteBottom - noteBorderV;
            float borderLeft = noteLeft + noteBorderH;
            float borderRight = noteRight - noteBorderH;

            // Check if there's enough space for inner area (matching shader lines 96-100)
            if (borderTop < borderBottom && borderLeft < borderRight)
            {
                // Make the gradient effect 2x more noticeable
                System.Drawing.Color innerColorL = System.Drawing.Color.FromArgb(
                    colorL.a,
                    Math.Max(0, Math.Min(255, colorL.r + 50)), // 2x more: +0.2f * 255 ≈ +50
                    Math.Max(0, Math.Min(255, colorL.g + 50)),
                    Math.Max(0, Math.Min(255, colorL.b + 50))
                );
                System.Drawing.Color innerColorR = System.Drawing.Color.FromArgb(
                    colorR.a,
                    Math.Max(0, Math.Min(255, colorR.r - 152)), // 2x more: -0.6f * 255 ≈ -152
                    Math.Max(0, Math.Min(255, colorR.g - 152)),
                    Math.Max(0, Math.Min(255, colorR.b - 152))
                );

                float innerWidth = borderRight - borderLeft;
                float innerHeight = borderBottom - borderTop;

                // Draw inner gradient area (matching shader triangles 3 & 4)
                DrawGradientRectangle(graphics, borderLeft, borderTop, innerWidth, innerHeight, innerColorL, innerColorR);
            }

            // NOTE: No additional outline drawing - the border effect comes from the shadow + inner area combination
            // This exactly matches the manual rendering which creates the border through the two-layer approach
        }

        /// <summary>
        /// Draw a horizontal gradient rectangle using System.Drawing
        /// </summary>
        private void DrawGradientRectangle(System.Drawing.Graphics graphics, float x, float y, float width, float height,
            System.Drawing.Color colorLeft, System.Drawing.Color colorRight)
        {
            if (width <= 0 || height <= 0) return;

            var rect = new System.Drawing.RectangleF(x, y, width, height);

            // Create linear gradient brush
            using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
                new System.Drawing.PointF(x, y),
                new System.Drawing.PointF(x + width, y),
                colorLeft,
                colorRight))
            {
                graphics.FillRectangle(brush, rect);
            }
        }

        /// <summary>
        /// Render a note using the pre-generated texture with proper border preservation (9-slice)
        /// </summary>
        private void RenderNoteWithTexture(RaylibRenderNote note, float x, float y, float width, float height)
        {
            // The note.png is white, so we can tint it with the note's color
            RaylibColor tint = note.colorLeft;

            // FORCE full opacity to prevent see-through notes
            tint.a = 255;

            // Ensure minimum size for visibility (increased from 1 pixel)
            float minSize = Math.Max(2, screenHeight * 0.001f); // At least 2 pixels or 0.1% of screen height
            if (width < minSize) width = minSize;
            if (height < minSize) height = minSize;

            try
            {
                // Calculate border size exactly like the original shader
                float noteBorder = 0.00091f;
                float borderH = Math.Max(1, (float)Math.Round(noteBorder * screenWidth) / screenWidth * screenWidth);
                float borderV = Math.Max(1, (float)Math.Round(noteBorder * screenHeight) / screenHeight * screenHeight / (screenHeight / (float)screenWidth));

                // For very short notes, make them slightly darker but still visible
                // Removed the extreme darkening that made notes almost invisible
                if (height < borderV * 4) // If note is very short
                {
                    // Make short notes darker but not too dark - keep them visible
                    tint = new RaylibColor(
                        (byte)(tint.r * 0.7f), // Darker but still visible (70% instead of 15%)
                        (byte)(tint.g * 0.7f),
                        (byte)(tint.b * 0.7f),
                        255 // Ensure full opacity
                    );
                }

                // For very small notes, still use texture but with simple stretching
                if (width <= borderH * 2 || height <= borderV * 2)
                {
                    // For tiny notes, use simple texture stretching (no 9-slice)
                    var sourceRect = new Rectangle(0, 0, noteTexture.width, noteTexture.height);
                    var destRect = new Rectangle(x, y, width, height);
                    Raylib.DrawTexturePro(noteTexture, sourceRect, destRect, new RaylibVector2(0, 0), 0.0f, tint);
                    return;
                }

                // For larger notes, use 9-slice rendering to preserve border appearance
                // Calculate texture border sizes using the same method as PNG generation
                float texBorderH = Math.Max(1, (float)Math.Round(noteBorder * noteTexture.width) / noteTexture.width * noteTexture.width);
                float texBorderV = Math.Max(1, (float)Math.Round(noteBorder * noteTexture.height) / noteTexture.height * noteTexture.height / (noteTexture.height / (float)noteTexture.width));

                // Render 9 slices:
                // Top-left corner
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(0, 0, texBorderH, texBorderV),
                    new Rectangle(x, y, borderH, borderV),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Top edge (stretched horizontally)
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(texBorderH, 0, noteTexture.width - texBorderH * 2, texBorderV),
                    new Rectangle(x + borderH, y, width - borderH * 2, borderV),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Top-right corner
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(noteTexture.width - texBorderH, 0, texBorderH, texBorderV),
                    new Rectangle(x + width - borderH, y, borderH, borderV),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Left edge (stretched vertically)
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(0, texBorderV, texBorderH, noteTexture.height - texBorderV * 2),
                    new Rectangle(x, y + borderV, borderH, height - borderV * 2),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Center (stretched both ways)
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(texBorderH, texBorderV, noteTexture.width - texBorderH * 2, noteTexture.height - texBorderV * 2),
                    new Rectangle(x + borderH, y + borderV, width - borderH * 2, height - borderV * 2),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Right edge (stretched vertically)
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(noteTexture.width - texBorderH, texBorderV, texBorderH, noteTexture.height - texBorderV * 2),
                    new Rectangle(x + width - borderH, y + borderV, borderH, height - borderV * 2),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Bottom-left corner
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(0, noteTexture.height - texBorderV, texBorderH, texBorderV),
                    new Rectangle(x, y + height - borderV, borderH, borderV),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Bottom edge (stretched horizontally)
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(texBorderH, noteTexture.height - texBorderV, noteTexture.width - texBorderH * 2, texBorderV),
                    new Rectangle(x + borderH, y + height - borderV, width - borderH * 2, borderV),
                    new RaylibVector2(0, 0), 0.0f, tint);

                // Bottom-right corner
                Raylib.DrawTexturePro(noteTexture,
                    new Rectangle(noteTexture.width - texBorderH, noteTexture.height - texBorderV, texBorderH, texBorderV),
                    new Rectangle(x + width - borderH, y + height - borderV, borderH, borderV),
                    new RaylibVector2(0, 0), 0.0f, tint);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CRITICAL ERROR: Texture rendering failed: {ex.Message}");
                // NO FALLBACK - texture-only rendering required
                return;
            }
        }

        /// <summary>
        /// Try to load the note texture for efficient rendering
        /// </summary>
        private void TryLoadNoteTexture()
        {
            try
            {
                if (!string.IsNullOrEmpty(noteTexturePath) && System.IO.File.Exists(noteTexturePath))
                {
                    Console.WriteLine($"Attempting to load note texture: {noteTexturePath}");
                    noteTexture = Raylib.LoadTexture(noteTexturePath);
                    useNoteTexture = noteTexture.id > 0;

                    if (useNoteTexture)
                    {
                        Console.WriteLine($"Successfully loaded note texture: {noteTexturePath} (ID: {noteTexture.id}, Size: {noteTexture.width}x{noteTexture.height})");
                    }
                    else
                    {
                        Console.WriteLine($"Failed to load note texture - texture ID is 0. Falling back to manual rendering");
                    }
                }
                else
                {
                    Console.WriteLine($"Note texture file not found: {noteTexturePath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading note texture: {ex.Message}");
                useNoteTexture = false;
            }
        }

        /// <summary>
        /// Initialize note texture system - call this after raylib is initialized
        /// </summary>
        public void InitializeNoteTexture()
        {
            // Check if note.png already exists
            string executableDir = System.IO.Path.GetDirectoryName(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
            noteTexturePath = System.IO.Path.Combine(executableDir, "note.png");

            if (System.IO.File.Exists(noteTexturePath))
            {
                TryLoadNoteTexture();
            }
        }

        public void Dispose()
        {
            // Cleanup texture if loaded
            if (useNoteTexture && noteTexture.id > 0)
            {
                Raylib.UnloadTexture(noteTexture);
            }
        }
    }
}
